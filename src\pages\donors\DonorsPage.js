import { useContext, useEffect, useState } from "react";
import {
  Box,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  IconButton,
  InputAdornment,
  FormControl,
  Grid,
  TextField,
  Typography,
  Menu,
  MenuItem,
} from "@mui/material";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import Icon from "src/@core/components/icon";
import Button from "@mui/material/Button";
import { DataGrid } from "@mui/x-data-grid";
import SearchIcon from "@mui/icons-material/Search";
import axios from "axios";
import { Controller, useForm } from "react-hook-form";
import FallbackSpinner from "src/@core/components/spinner";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import useColumns from "./Columns";
import DeleteDialog from "./DeleteDialog";
import * as XLSX from "xlsx";
import DonorDialog from "./DonorDialog";
import authConfig from "src/configs/auth";
import AdvancedSearch from "./AdvancedSearch";
import BlankLayout from "src/@core/layouts/BlankLayout";
import { useRBAC } from "../permission/RBACContext";
import { useRouter } from "next/router";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import ImportExportDonors from "../approve-donors/ImportDialog";
import { Upload } from "@mui/icons-material";
import FileDownloadSharpIcon from "@mui/icons-material/FileDownloadSharp";
import FileUploadSharpIcon from "@mui/icons-material/FileUploadSharp";

const DonorsPage = () => {
  const {
    canMenuPage,
    canMenuPageSection,
    canMenuPageSectionField,
    rbacRoles,
  } = useRBAC();
  const router = useRouter();

  const canAccessDonors = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.DONORS, requiredPermission);

  const [userList, setUserList] = useState([]);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const {
    user,
    donorDetails,
    setListValues,
    setDonorDetails,
    getAllListValuesByListNameId,
  } = useContext(AuthContext);

  const [importOpen, setImportOpen] = useState(false);

  const [searchKeyword, setSearchKeyword] = useState("");
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState(null);
  const [rowCount, setRowCount] = useState();
  const [tagsList, setTagsList] = useState([]);
  const [keyword, setKeyword] = useState("");
  const [loading, setLoading] = useState(false);
  const [openAdvancedSearch, setOpenAdvancedSearch] = useState(false);
  const [searchingState, setSearchingState] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [tenantsList, setTenantsList] = useState([]);
  const [valuesUpdate, setValuesUpdate] = useState(false);
  const [menu, setMenu] = useState(null);
  const [sendMessageDialog, setSendMessageDialog] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [emailsList, setEmailsList] = useState([]);
  const [selectedTemplateName, setSelectedTemplateName] = useState("");
  const [sendingMessage, setSendingMessage] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [messageStatus, setMessageStatus] = useState(null);
  const [communicationMode, setCommunicationMode] = useState(""); // "WHATSAPP", "EMAIL", or "SMS"

  const { control } = useForm();

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  const [selectedRows, setSelectedRows] = useState([]);

  const handleSelectionModelChange = (newSelectionModel) => {
    setSelectedRows(newSelectionModel);
  };

  // Fetch tenants list for SUPER_ADMIN
  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.organisationsEndpoint) + "/TENANT",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setTenantsList(
          res.data.map((item) => ({
            value: item.id,
            key: item.name,
          }))
        );
      })
      .catch(() => {
        setTenantsList([]);
      });
  }, []);

  // Fetch WhatsApp templates
  useEffect(() => {
    const token =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.pKpfhwXgjSNRAJt2XozZI9_ezpj025J36UBObgQDf5k";

    const fetchTemplates = async () => {
      try {
        const response = await fetch(
          "https://live-mt-server.wati.io/321589/api/v1/getMessageTemplates",
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        const templateData = data.messageTemplates.map((template) => ({
          key: template?.elementName,
          value: template?.id,
          parameters: template.parameters || [], // Keep additional data if needed
          status: template.status,
        }));
        const filteredTemplates = templateData.filter(
          (template) => template.status !== "DELETED"
        );

        setTemplates(filteredTemplates);
      } catch (error) {
        console.error("Error fetching templates:", error);
      }
    };

    fetchTemplates();
  }, []);

  // Initialize emailsList with data
  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.donorGroupsEndpoint) + "/email-templates",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmailsList(
          res.data.map((item) => ({
            value: item.id,
            key: item.templateName,
          }))
        );
      })
      .catch(() => {
        setEmailsList([]);
      });
  }, []);

  useEffect(() => {
    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_VALUES",
    })
      .then((res) => {
        setListValues(res.data.data);
        window.localStorage.setItem(
          authConfig.listValues,
          JSON.stringify(res.data.data)
        );
      })
      .catch((err) => console.log("List values error", err));
  }, [valuesUpdate]);

  // Fetch Donor list
  const fetchDonors = async (currentPage, currentPageSize, selectedFilters) => {
    setLoading(true);
    const url = getUrl(authConfig.donorsEndpoint) + "/all/donor";
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
    };

    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      data[key] = filter.value;
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data && response.data.donorsResponseList.length > 0) {
        setUserList(response.data.donorsResponseList);
        setRowCount(
          response.data?.rowCount || response.data.donorsResponseList.length
        );
      }
    } catch (err) {
      console.error("Failed to fetch donors:", err);
    }

    setLoading(false);
  };

  useEffect(() => {
    fetchDonors(page, pageSize, selectedFilters);
  }, [page, pageSize, selectedFilters]);

  // Handler functions
  const handleSendWhatsAppClick = () => {
    setCommunicationMode("WHATSAPP");
    setSendMessageDialog(true);
  };

  const handleSendEmailClick = () => {
    setCommunicationMode("EMAIL");
    setSendMessageDialog(true);
  };

  const handleSendSMSClick = () => {
    setCommunicationMode("SMS");
    setSendMessageDialog(true);
  };

  const handleCloseSendMessageDialog = () => {
    setSendMessageDialog(false);
    setSelectedTemplateName("");
    setSendingMessage(false);
    setCommunicationMode("");
  };

  const handleSendMessageSuccess = () => {
    handleCloseSendMessageDialog();
    const communicationType =
      communicationMode === "EMAIL" ? "Email" :
      communicationMode === "SMS" ? "SMS" : "WhatsApp";
    const message = `
        <div>
          <h3>Message sent successfully to donor via ${communicationType}.</h3>
        </div>
      `;
    setDialogMessage(message);
    setMessageStatus("info");
  };

  const handleSendMessageFailure = (err) => {
    handleCloseSendMessageDialog();
    const communicationType =
      communicationMode === "EMAIL" ? "Email" :
      communicationMode === "SMS" ? "SMS" : "WhatsApp";
    const message = `
        <div>
          <h3>Failed to send message via ${communicationType}. Please try again later.</h3>
        </div>
      `;
    setDialogMessage(message);
    setMessageStatus("error");
  };

  const handleSendMessage = async () => {
    // For SMS, we don't need template selection
    if (communicationMode !== "SMS" && (!selectedTemplateName || !currentRow)) {
      const templateType =
        communicationMode === "EMAIL" ? "email template" : "template";
      setDialogMessage(`Please select a ${templateType}`);
      setMessageStatus("error");
      return;
    }

    if (!currentRow) {
      setDialogMessage("No donor selected");
      setMessageStatus("error");
      return;
    }

    setSendingMessage(true);

    const url =
      getUrl(authConfig.donorsEndpoint) +
      "/send-communication/" +
      currentRow?.id;

    const data = {
      communicationModeEnums: communicationMode,
      ...(communicationMode !== "SMS" && { templateIdOrName: selectedTemplateName }),
    };

    const headers = getAuthorizationHeaders();

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data === "Communication sent successfully") {
        handleSendMessageSuccess();
      } else {
        handleSendMessageFailure("Failed to send message to donor");
      }
      setSendingMessage(false);
    } catch (error) {
      handleSendMessageFailure(error);
      setSendingMessage(false);
    }
  };

  // Columns
  const columns = useColumns({
    currentRow,
    setCurrentRow,
    setOpenDialog,
    tenantsList,
    menu,
    setMenu,
    setOpenDeleteDialog,
    setSendWhatsAppDialog: handleSendWhatsAppClick,
    setSendEmailDialog: handleSendEmailClick,
    setSendSMSDialog: handleSendSMSClick,
  });

  const handleExportDonors = () => {
    const url = getUrl(authConfig.donorImportEndpoint) + "/export-donors";
    const headers = getAuthorizationHeaders();

    const today = new Date();
    const day = String(today.getDate()).padStart(2, "0");
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const year = today.getFullYear();
    const formattedDate = `${day}-${month}-${year}`;

    axios({
      method: "post",
      url: url,
      headers: headers,
      data: selectedRows,
      responseType: "arraybuffer",
    })
      .then((res) => {
        const workbook = XLSX.read(res.data, { type: "array" });

        const fileName = `donor_export-${formattedDate}.xlsx`;
        XLSX.writeFile(workbook, fileName);
      })
      .catch((error) => {
        console.error("Error fetching users:", error);
      });
  };

  // Handlers
  const handleSearch = (e) => {
    setKeyword(e.target.value);
    setSearchKeyword(e.target.value);
    setPage(1);
  };

  const handleOpenDialog = () => {
    setCurrentRow(null);
    setOpenDialog(true);
  };

  const handleOpenImportDialog = () => {
    setImportOpen(true);
  };

  const handleAdvancedSearch = () => {
    setOpenAdvancedSearch(!openAdvancedSearch);
  };

  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchingState(false);
  };

  const handleApplyFilters = (filters) => {
    setSelectedFilters(filters);
    setSearchingState(true);
  };

  const handleRemoveFilter = (filterKey) => {
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== filterKey)
    );
  };

  const handlePageChange = (newPage) => {
    setPage(newPage + 1);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    setPage(1);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setDonorDetails(null);
  };

  const handleCloseImportDialog = () => {
    setImportOpen(false);
  };

  const handleError = (error) => {
    console.error("Donors page error:", error);
  };

  const handleMessageStatusClose = () => {
    setMessageStatus(null);
    setDialogMessage("");
  };

  useEffect(() => {
    getAllListValuesByListNameId(
      authConfig.tagsListNameId,
      (data) =>
        setTagsList(
          data?.listValues.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );
  }, [authConfig, valuesUpdate]);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessDonors(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);
  if (canAccessDonors(PERMISSIONS.READ)) {
    return (
      <Grid>
        <Box
          sx={{
            py: 3,
            px: 6,
            rowGap: 2,
            columnGap: 4,
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={4}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Donors
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Grid container spacing={2} justifyContent="flex-end">
                {/* <Grid item xs={12} sm={4}>
                      <FormControl fullWidth>
                        <TextField
                          placeholder="Search by Donor name "
                          value={keyword}
                          onChange={handleSearch}
                          size="small"
                          sx={{
                            height: 40,
                            background: "#fafbfc",
                            borderRadius: 1,
                            "& .MuiOutlinedInput-root": {
                              borderRadius: 1,
                              height: 40,
                              minHeight: 40,
                              paddingRight: 0,
                              background: "#fff",
                            },
                            "& fieldset": {
                              borderColor: "#e0e0e0",
                            },
                            "& input": {
                              height: 40,
                              padding: "0 14px",
                              boxSizing: "border-box",
                            },
                          }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="start">
                                <SearchIcon
                                  sx={{ cursor: "pointer", color: "#757575" }}
                                />
                              </InputAdornment>
                            ),
                          }}
                        />
                      </FormControl>
                    </Grid> */}
                {/* <Grid item xs="auto">
                      <Box
                        sx={{
                          minHeight: 40,
                          height: 40,
                          minWidth: 40,
                          width: 40,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          background: "#23236A",
                          borderRadius: 1,
                          ml: 1,
                        }}
                      >
                        <AdvancedSearch
                          open={openAdvancedSearch}
                          toggle={handleAdvancedSearch}
                          searchingState={searchingState}
                          setSearchingState={setSearchingState}
                          selectedFilters={selectedFilters}
                          clearAllFilters={clearAllFilters}
                          onApplyFilters={handleApplyFilters}
                          tenantsList={tenantsList}
                        />
                      </Box>
                    </Grid> */}
                <Grid item xs="auto">
                  <Button
                    variant="contained"
                    onClick={handleExportDonors}
                    startIcon={<FileUploadSharpIcon />}
                    disabled={!selectedRows || selectedRows.length === 0}
                    sx={{
                      height: 40,
                      minWidth: 80,
                      px: 3,
                      ml: 1,
                      background: "#23236A",
                      color: "#fff",
                      fontWeight: 600,
                      borderRadius: 1,
                      boxShadow: "none",
                      "&:hover": {
                        background: "#1a1a4f",
                        boxShadow: "none",
                      },
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    Export
                  </Button>
                </Grid>

                <Grid item xs="auto">
                  <Button
                    variant="contained"
                    startIcon={<FileDownloadSharpIcon />}
                    onClick={handleOpenImportDialog}
                    sx={{
                      height: 40,
                      minWidth: 80,
                      px: 3,
                      ml: 1,
                      background: "#23236A",
                      color: "#fff",
                      fontWeight: 600,
                      borderRadius: 1,
                      boxShadow: "none",
                      "&:hover": {
                        background: "#1a1a4f",
                        boxShadow: "none",
                      },
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    Import
                  </Button>
                </Grid>
                <Grid item xs="auto">
                  <Button
                    variant="contained"
                    onClick={handleOpenDialog}
                    sx={{
                      height: 40,
                      minWidth: 80,
                      px: 3,
                      ml: 1,
                      background: "#23236A",
                      color: "#fff",
                      fontWeight: 600,
                      borderRadius: 1,
                      boxShadow: "none",
                      "&:hover": {
                        background: "#1a1a4f",
                        boxShadow: "none",
                      },
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    Add
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>
        <Divider />
        <CardContent>
          {/* Filter Chips */}
          <Box sx={{ display: "flex", flexWrap: "wrap", mb: 2 }}>
            {selectedFilters.map((filter) => (
              <Chip
                key={filter.key}
                label={`${filter.label}: ${filter.value}`}
                onDelete={() => handleRemoveFilter(filter.key)}
                sx={{ mr: 1, mb: 1 }}
              />
            ))}
          </Box>
          {/* DataGrid Table */}
          <div style={{ height: 430, width: "100%" }}>
            <DataGrid
              rows={userList}
              columns={columns}
              checkboxSelection
              onSelectionModelChange={handleSelectionModelChange}
              pageSize={pageSize}
              page={page - 1}
              rowsPerPageOptions={rowsPerPageOptions}
              rowCount={rowCount}
              paginationMode="server"
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              rowHeight={38}
              headerHeight={38}
            />
          </div>
        </CardContent>
        <DonorDialog
          open={openDialog}
          onClose={handleCloseDialog}
          formData={donorDetails}
          tenantsList={tenantsList}
          tagsList={tagsList}
          fetchDonors={fetchDonors}
          page={page}
          pageSize={pageSize}
          searchKeyword={selectedFilters}
          setValuesUpdate={setValuesUpdate}
        />

        <ImportExportDonors
          open={importOpen}
          onClose={handleCloseImportDialog}
        />

        <DeleteDialog
          open={openDeleteDialog}
          onClose={handleCloseDeleteDialog}
          data={currentRow}
          fetchDonors={fetchDonors}
          page={page}
          pageSize={pageSize}
          searchKeyword={selectedFilters}
        />

        {/* Message Status Dialog */}
        <Dialog
          open={messageStatus !== null}
          onClose={handleMessageStatusClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.text.primary.main,
            },
          }}
        >
          <Box
            sx={{
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
                fontWeight={"bold"}
                dangerouslySetInnerHTML={{ __html: dialogMessage }}
              />
            </DialogContent>
            <DialogActions
              sx={{
                display: "flex",
                justifyContent: "center",
              }}
            >
              <Button onClick={handleMessageStatusClose} color="primary">
                OK
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

        {/* Send Message Dialog */}
        <Dialog
          open={sendMessageDialog}
          onClose={handleCloseSendMessageDialog}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              width: "400px",
              height: "250px",
            },
          }}
        >
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.75, 4)} !important`,
              display: "flex",
              alignItems: "center",
              justifyContent: { xs: "start" },
              fontSize: { xs: 19, md: 20 },
              marginLeft: { xl: 1.5, lg: 1.5, md: 1.5, sm: 1.5, xs: 1.5 },
            }}
            textAlign={"center"}
          >
            {communicationMode === "SMS" ? "Send Text Message" :
             `Send Message via ${communicationMode === "EMAIL" ? "Email" : "WhatsApp"}`}
          </DialogTitle>

          <Box sx={{ position: "absolute", top: "8px", right: "24px" }}>
            <IconButton
              size="small"
              onClick={handleCloseSendMessageDialog}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>

          <DialogContent>
            <Box sx={{ mt: 2 }}>
              {communicationMode === "SMS" ? (
                <Typography variant="body1" sx={{ textAlign: "center", py: 2 }}>
                  Are you sure you want to send message?
                </Typography>
              ) : (
                <SelectAutoComplete
                  id="template-select"
                  label={`Select a ${
                    communicationMode === "EMAIL" ? "Email Template" : "Template"
                  }`}
                  nameArray={
                    communicationMode === "EMAIL" ? emailsList : templates
                  }
                  value={
                    // Find the template value that corresponds to the selected key
                    communicationMode === "EMAIL"
                      ? emailsList.find(
                          (email) => email.key === selectedTemplateName
                        )?.value || null
                      : templates.find(
                          (template) => template.key === selectedTemplateName
                        )?.value || null
                  }
                  onChange={(event) => {
                    // Find the selected template object to get the key
                    const selectedArray =
                      communicationMode === "EMAIL" ? emailsList : templates;
                    const selectedTemplate = selectedArray.find(
                      (item) => item.value === event.target.value
                    );
                    const templateKey = selectedTemplate
                      ? selectedTemplate.key
                      : event.target.value;
                    setSelectedTemplateName(templateKey);
                  }}
                />
              )}
            </Box>
          </DialogContent>

          <DialogActions
            sx={{
              justifyContent: "end",
              borderTop: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(2.5)} !important`,
              marginRight: { xl: 4, lg: 3.5, md: 3.5, sm: 3.5, xs: 3.5 },
            }}
          >
            <Button onClick={handleCloseSendMessageDialog}>Cancel</Button>
            <Button
              onClick={handleSendMessage}
              disabled={
                communicationMode === "SMS"
                  ? sendingMessage
                  : !selectedTemplateName || sendingMessage
              }
              variant="contained"
              color="primary"
            >
              {sendingMessage ? "Sending..." : "Send"}
            </Button>
          </DialogActions>
        </Dialog>
      </Grid>
    );
  } else {
    return null;
  }
};

export default DonorsPage;
